# Generated by Django 4.2.20 on 2025-05-12 13:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0010_rename_questionanswer_quiz"),
        ("users", "0005_student_otp_student_otp_created_at"),
    ]

    operations = [
        migrations.CreateModel(
            name="StudentPerformance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quiz_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Score achieved on the quiz (percentage)",
                        max_digits=5,
                    ),
                ),
                (
                    "attempts",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of attempts made on the quiz"
                    ),
                ),
                (
                    "remarks",
                    models.TextField(
                        blank=True,
                        help_text="Feedback or comments on student performance",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_performances",
                        to="documents.document",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performances",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Performance",
                "verbose_name_plural": "Student Performances",
                "ordering": ["-updated_at"],
                "indexes": [
                    models.Index(
                        fields=["student", "document"],
                        name="users_stude_student_4c8d6a_idx",
                    ),
                    models.Index(
                        fields=["quiz_score"], name="users_stude_quiz_sc_4f6341_idx"
                    ),
                ],
                "unique_together": {("student", "document")},
            },
        ),
    ]
