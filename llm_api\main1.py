from fastapi import FastAPI, HTTPException, Depends, status, UploadFile, File
from typing import Optional, List, Dict, Any
import os
import json
import re
import logging
import sys

# Add the current directory to the path so we can import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv

import httpx
from preprocessing import preprocess_pdf, create_embeddings

# Import models - use absolute imports instead of relative
from models import (
    InferenceRequest, InferenceResponse,
    DocumentProcessResponse, BlueprintProcessRequest, BlueprintProcessResponse,
    BlueprintTopic, UserInfo, QuizGenerationRequest, QuizGenerationResponse, QuizQuestion
)
from auth import get_current_user
from llms import get_openai_response, get_gemini_response

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


# Load environment variables
load_dotenv()

# Get Django server URL from environment variables
DJANGO_SERVER_URL = os.getenv("DJANGO_SERVER_URL", "http://localhost:8000")

# Initialize FastAPI app
app = FastAPI(
    title="LLM Inference API",
    description="API for LLM inference with user authentication and usage tracking",
    version="1.0.0"
)

# Add middleware to log all requests
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all incoming requests to the FastAPI server."""
    # Get request details
    request_id = id(request)
    client_host = request.client.host if request.client else "unknown"
    request_path = request.url.path
    request_method = request.method
    request_headers = dict(request.headers)

    # Log the request
    logging.info(f"Request {request_id} received: {request_method} {request_path} from {client_host}")
    logging.info(f"Request {request_id} headers: {request_headers}")

    # Process the request
    try:
        response = await call_next(request)
        logging.info(f"Request {request_id} completed with status code: {response.status_code}")
        return response
    except Exception as e:
        logging.error(f"Request {request_id} failed with error: {str(e)}")
        raise




@app.post("/infer", response_model=InferenceResponse)
async def infer(
    request: InferenceRequest,
    user: UserInfo = Depends(get_current_user)
):
    """
    Make an inference request to the specified LLM model.

    - **model**: The LLM to use ("openai" or "gemini")
    - **message**: The message/prompt for the model
    - **context**: Optional context to provide to the model

    Requires authentication via Bearer token.
    """
    model_name = request.model.lower()

    try:
        if model_name == "openai":
            response, tokens = await get_openai_response(request.message, request.context)
        elif model_name == "gemini":
            response, tokens = await get_gemini_response(request.message, request.context)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported model. Please use 'openai' or 'gemini'"
            )

        # Get usage stats from Django server
        async with httpx.AsyncClient() as client:
            stats_response = await client.get(
                f"{DJANGO_SERVER_URL}/api/users/{user.id}/usage-stats/",
                headers={"Authorization": f"Bearer {user.id}"}  # Using user ID as a simple auth mechanism
            )

            if stats_response.status_code == 200:
                usage_stats = stats_response.json()
            else:
                usage_stats = None

        return InferenceResponse(
            response=response,
            model=model_name,
            tokens=tokens,
            usage_stats=usage_stats
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@app.post("/process-document/{document_id}", response_model=DocumentProcessResponse)
async def process_document(
    document_id: int,
    file: UploadFile = File(...),
    user: UserInfo = Depends(get_current_user)
):
    """
    Process an uploaded document:
    1. Extract and clean text from PDF
    2. Create embeddings for text chunks
    3. Send embeddings to Django server for storage
    """
    try:
        # Read file content
        content = await file.read()

        # Process PDF
        clean_text = await preprocess_pdf(content)

        # Generate embeddings and text chunks
        text_chunks, embeddings = create_embeddings(clean_text)

        # Create a list of text chunks with their embeddings
        chunks = []
        for idx, (chunk, emb) in enumerate(zip(text_chunks, embeddings)):
            chunks.append({
                "text": chunk,
                "embedding": emb,
                "chunk_number": idx
            })

        # Send the embeddings to Django server for storage
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/store-embeddings/",
                json={
                    "document_id": document_id,
                    "chunks": chunks
                },
                headers={"Authorization": f"Bearer {user.id}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to store embeddings in Django server: {response.text}"
                )

        return DocumentProcessResponse(
            message="Document processed successfully",
            document_id=document_id,
            num_chunks=len(text_chunks),
            status="completed"
        )

    except Exception as e:
        # Notify Django server about the failure
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{DJANGO_SERVER_URL}/api/documents/{document_id}/update-status/",
                    json={
                        "status": "failed",
                        "error_message": str(e)
                    },
                    headers={"Authorization": f"Token {user.id}"}
                )
        except Exception:
            # If we can't notify Django, we still want to return an error to the client
            pass

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing document: {str(e)}"
        )


@app.post("/process-blueprint/{document_id}", response_model=BlueprintProcessResponse)
async def process_blueprint(
    document_id: int,
    blueprint_request: BlueprintProcessRequest,
    user: UserInfo = Depends(get_current_user)
):
    """
    Process a document blueprint:
    1. Use LLM to identify topics and their weightage
    2. Send topics to Django server for storage and linking with document embeddings

    Request body:
    - document_id: ID of the document to process
    - user_id: ID of the user making the request
    - blueprint_text: The blueprint text to analyze
    - llm_model: The LLM model to use ("openai" or "gemini")
    """
    try:
        # Use the specified LLM to identify topics and their weightage
        topics = await identify_topics_with_llm(
            blueprint_request.blueprint_text,
            user,
            blueprint_request.llm_model
        )

        # Convert topics to the expected format
        topic_list = [
            BlueprintTopic(title=topic['title'], weightage=topic['weightage'])
            for topic in topics
        ]

        # Send the topics to Django server for storage and linking
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/store-topics/",
                json={
                    "document_id": document_id,
                    "topics": [{"title": t.title, "weightage": t.weightage} for t in topic_list]
                },
                headers={"Authorization": f"Bearer {user.id}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to store topics in Django server: {response.text}"
                )

        return BlueprintProcessResponse(
            message="Blueprint processed successfully",
            document_id=document_id,
            topics=topic_list,
            status="completed"
        )

    except Exception as e:
        # Notify Django server about the failure
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{DJANGO_SERVER_URL}/api/documents/{document_id}/update-status/",
                    json={
                        "status": "failed",
                        "error_message": str(e)
                    },
                    headers={"Authorization": f"Token {user.id}"}
                )
        except Exception:
            # If we can't notify Django, we still want to return an error to the client
            pass

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing blueprint: {str(e)}"
        )


async def identify_topics_with_llm(blueprint_text: str, user: UserInfo, llm_model: str = "openai") -> list:
    """
    Use LLM to identify topics and their weightage from blueprint text.
    Returns a list of dictionaries with 'title' and 'weightage' keys.
    Uses the existing infer endpoint for LLM requests.

    Parameters:
    - blueprint_text: The text to analyze
    - user: The user making the request
    - llm_model: The LLM model to use ("openai" or "gemini")
    """
    prompt = f"""
    Analyze the following document blueprint and identify the main topics covered.
    For each topic, assign a weightage (percentage importance) that reflects its significance in the document.
    The sum of all weightages should be 100%.

    Return the result as a JSON array of objects, each with 'title' and 'weightage' properties.
    Example: [{"title": "Introduction to AI", "weightage": 25.5}, {"title": "Machine Learning Basics", "weightage": 30.0}]

    Blueprint:
    {blueprint_text}
    """

    try:
        # Create a request to the infer endpoint
        request = InferenceRequest(
            model=llm_model,  # Use the specified model
            message=prompt,
            context="You are a document analysis assistant that identifies topics and their importance."
        )

        # Call the infer function directly
        response = await infer(request, user)

        # Extract the response text
        response_text = response.response

        # Extract and parse the JSON response
        import json
        import re

        # Find JSON array in the response
        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            topics = json.loads(json_str)
        else:
            # Fallback if no JSON array is found
            topics = [{"title": "General Content", "weightage": 100.0}]

        return topics
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.error(f"Error in identify_topics_with_llm: {str(e)}")

        # Fallback in case of error
        return [{"title": "General Content", "weightage": 100.0}]



async def find_relevant_embeddings(topic: str, document_id: int, user: UserInfo) -> list:
    """
    Find embeddings relevant to a topic using semantic search.
    Makes a request to Django server to perform the search.

    Parameters:
    - topic: The topic to search for
    - document_id: The document ID to search in
    - user: The user making the request

    Returns:
    - List of embedding IDs that are relevant to the topic
    """
    try:
        # Make a request to Django server to find relevant embeddings
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/find-relevant-embeddings/",
                json={
                    "topic": topic,
                    "document_id": document_id
                },
                headers={"Authorization": f"Bearer {user.id}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to find relevant embeddings: {response.text}"
                )

            return response.json()["embedding_ids"]
    except Exception as e:
        logging.error(f"Error in find_relevant_embeddings: {str(e)}")
        return []



@app.get("/test-django-connection")
async def test_django_connection():
    """
    Test the connection to the Django server.
    """
    try:
        # First, try a public endpoint that doesn't require authentication
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{DJANGO_SERVER_URL}/admin/login/")

            if response.status_code == 200:
                return {"status": "success", "message": "Connection to Django server successful (public endpoint)"}
            else:
                return {
                    "status": "error",
                    "message": f"Connection to Django server failed with status code {response.status_code} (public endpoint)",
                    "response": response.text[:100]  # Limit response text to first 100 chars
                }
    except Exception as e:
        return {"status": "error", "message": f"Connection to Django server failed: {str(e)}"}



@app.get("/test-auth")
async def test_auth(user: UserInfo = Depends(get_current_user)):
    """
    Test authentication using the actual authentication mechanism from auth.py.

    This endpoint uses the same authentication flow as the other API endpoints,
    requiring a proper Authorization header with a Bearer token.

    If authentication is successful, it returns information about the authenticated user.
    """
    try:
        return {
            "status": "success",
            "message": "Authentication successful",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email
            },
            "auth_details": {
                "django_server_url": DJANGO_SERVER_URL,
                "auth_method": "Bearer token via get_current_user dependency"
            }
        }
    except Exception as e:
        # This should not be reached as get_current_user will raise an HTTPException
        # if authentication fails, but we include it for completeness
        return {
            "status": "error",
            "message": f"Unexpected error in authentication: {str(e)}"
        }


@app.get("/test-network")
async def test_network():
    """
    Test network connectivity to various endpoints.
    """
    results = {}

    # Test Django server with different host configurations
    hosts_to_test = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://0.0.0.0:8000"
    ]

    for host in hosts_to_test:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{host}/admin/login/", timeout=5.0)
                results[host] = {
                    "status": "success" if response.status_code == 200 else "error",
                    "status_code": response.status_code
                }
        except Exception as e:
            results[host] = {
                "status": "error",
                "message": str(e)
            }

    # Add current configuration
    results["current_config"] = {
        "DJANGO_SERVER_URL": DJANGO_SERVER_URL,
        "HOST": os.getenv("HOST", "0.0.0.0"),
        "PORT": os.getenv("PORT", "8001")
    }

    return results


@app.post("/generate-quiz/{document_id}", response_model=QuizGenerationResponse)
async def generate_quiz(
    document_id: int,
    quiz_request: QuizGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate quiz questions and answers based on document content:
    1. Retrieve all document embeddings for the specified document
    2. Group the text from the document embeddings
    3. Use LLM to generate questions and answers based on the content
    4. Return the generated questions and answers

    - **document_id**: ID of the document to generate quiz for
    - **llm_model**: The LLM model to use ("openai" or "gemini")
    - **num_questions**: Number of questions to generate (default: 5)

    Requires authentication via Bearer token.
    """
    try:
        # If no request body is provided, create a default one
        if quiz_request is None:
            quiz_request = QuizGenerationRequest(document_id=document_id)

        # Ensure document_id in path matches the one in request body
        if quiz_request.document_id != document_id:
            quiz_request.document_id = document_id

        # Get all document embeddings from Django server
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/embeddings/",
                headers={"Authorization": f"Bearer {user.id}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve document embeddings: {response.text}"
                )

            embeddings_data = response.json()

        # Extract text from embeddings and group by chunks
        # Sort by chunk_number to maintain document order
        text_chunks = []
        for embedding in sorted(embeddings_data, key=lambda x: x.get('chunk_number', 0)):
            text_chunks.append(embedding.get('text_chunk', ''))

        # Combine text chunks into a single document
        # Limit to first 10 chunks to avoid token limits
        combined_text = " ".join(text_chunks[:10])

        # Generate quiz questions using LLM
        questions = await generate_quiz_questions(
            combined_text,
            user,
            quiz_request.llm_model,
            quiz_request.num_questions
        )

        return QuizGenerationResponse(
            message="Quiz generated successfully",
            document_id=document_id,
            questions=questions['questions'],
            status="completed",
            model=quiz_request.llm_model,
            tokens=questions['tokens']
        )

    except Exception as e:
        logging.error(f"Error generating quiz: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating quiz: {str(e)}"
        )


async def generate_quiz_questions(text: str, user: UserInfo, llm_model: str = "openai", num_questions: int = 5) -> dict:
    """
    Use LLM to generate quiz questions and answers from text.
    Returns a dictionary with 'questions' and 'tokens' keys.
    Uses the existing infer endpoint for LLM requests.

    Parameters:
    - text: The text to generate questions from
    - user: The user making the request
    - llm_model: The LLM model to use ("openai" or "gemini")
    - num_questions: Number of questions to generate
    """
    prompt = f"""
    Based on the following text, generate {num_questions} quiz questions and their answers.
    The questions should test understanding of key concepts in the text.

    Return the result as a JSON array of objects, each with 'question' and 'answer' properties.
    Example: [{{"question": "What is the capital of France?", "answer": "The capital of France is Paris."}}]

    Text:
    {text}
    """

    try:
        # Create a request to the infer endpoint
        request = InferenceRequest(
            model=llm_model,
            message=prompt,
            context="You are an educational quiz generator that creates insightful questions and clear answers."
        )

        # Call the infer function directly
        response = await infer(request, user)

        # Extract the response text
        response_text = response.response

        # Extract and parse the JSON response
        import json
        import re

        # Find JSON array in the response
        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            quiz_data = json.loads(json_str)

            # Convert to QuizQuestion objects
            questions = [
                QuizQuestion(question=item['question'], answer=item['answer'])
                for item in quiz_data
            ]
        else:
            # Fallback if no JSON array is found
            questions = [
                QuizQuestion(
                    question=f"Question {i+1} about the document content?",
                    answer=f"This is a fallback answer for question {i+1}."
                )
                for i in range(num_questions)
            ]

        return {
            "questions": questions,
            "tokens": response.tokens
        }
    except Exception as e:
        # Log the error for debugging
        logging.error(f"Error in generate_quiz_questions: {str(e)}")

        # Fallback in case of error
        fallback_questions = [
            QuizQuestion(
                question=f"Question {i+1} about the document content?",
                answer=f"This is a fallback answer for question {i+1}."
            )
            for i in range(num_questions)
        ]

        return {
            "questions": fallback_questions,
            "tokens": 0
        }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)