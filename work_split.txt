##users 
sign in integration or email verification
profile edit

##chat  
gemini integration 
free user limits
efficient chat storage + history display and retrivel 

##documents
(embedding,flashcard, flowchart) api call and Store

##payments app + 3rd party integration


##fastapi
common gemini,chatgpt endpoint(params- intruction, context)
ocr(paid/pytesseract)
text extraction, cleaning 
embeddings
RAG