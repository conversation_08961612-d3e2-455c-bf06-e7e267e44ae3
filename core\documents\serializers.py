from rest_framework import serializers
from .models import Document, DocumentEmbedding, Flashcard, Flowchart, Quiz, BlueprintTopics

class DocumentSerializer(serializers.ModelSerializer):
    num_embeddings = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = ['id', 'title', 'file', 'uploaded_at', 'processing_status', 'error_message', 'num_embeddings']
        read_only_fields = ['processing_status', 'error_message']

    def get_num_embeddings(self, obj):
        return obj.embeddings.count() if obj.processing_status == 'completed' else 0

class DocumentEmbeddingSerializer(serializers.ModelSerializer):
    class Meta:
        model = DocumentEmbedding
        fields = '__all__'

class FlashcardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Flashcard
        fields = '__all__'

class FlowchartSerializer(serializers.ModelSerializer):
    class Meta:
        model = Flowchart
        fields = '__all__'

class QuizSerializer(serializers.ModelSerializer):
    class Meta:
        model = Quiz
        fields = '__all__'


class UploadBlueprintSerializer(serializers.Serializer):
    file = serializers.FileField()


class BlueprintTopicsSerializer(serializers.ModelSerializer):
    document_title = serializers.CharField(source='document.title', read_only=True)
    content_count = serializers.SerializerMethodField()

    class Meta:
        model = BlueprintTopics
        fields = ['id', 'document', 'document_title', 'title', 'weightage',
                 'content', 'content_count', 'created_at']
        read_only_fields = ['created_at', 'document_title', 'content_count']

    def get_content_count(self, obj):
        return obj.content.count()


class BlueprintTopicsDetailSerializer(BlueprintTopicsSerializer):
    """Detailed serializer that includes content details"""
    content_details = serializers.SerializerMethodField()

    class Meta(BlueprintTopicsSerializer.Meta):
        fields = BlueprintTopicsSerializer.Meta.fields + ['content_details']

    def get_content_details(self, obj):
        # Return the full text of each content chunk
        return [
            {
                'id': content.id,
                'chunk_number': content.chunk_number,
                'text': content.text_chunk,
                # Keep preview for backward compatibility
                'preview': content.text_chunk[:100] + '...' if len(content.text_chunk) > 100 else content.text_chunk
            }
            for content in obj.content.all().order_by('chunk_number')
        ]