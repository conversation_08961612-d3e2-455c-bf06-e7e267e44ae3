from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from documents.models import Document, DocumentEmbedding, BlueprintTopics
from django.core.files.uploadedfile import SimpleUploadedFile

class BlueprintTopicsTests(APITestCase):
    def setUp(self):
        # Create a test user
        self.User = get_user_model()
        self.user = self.User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
            is_email_verified=True
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create a test document
        self.document = Document.objects.create(
            user=self.user,
            title='Test Document',
            file=SimpleUploadedFile("dummy.txt", b"Test"),
            blueprint="This is a test blueprint for testing topics."
        )

        # Create some document embeddings
        self.embeddings = []
        for i in range(5):
            embedding = DocumentEmbedding.objects.create(
                document=self.document,
                text_chunk=f"This is test chunk {i}",
                embedding=[0.1, 0.2, 0.3],  # Simplified embedding
                chunk_number=i
            )
            self.embeddings.append(embedding)

        # Create some test topics
        self.topic1 = BlueprintTopics.objects.create(
            document=self.document,
            title="Topic 1",
            weightage=60.0
        )
        self.topic1.content.add(*self.embeddings[:3])

        self.topic2 = BlueprintTopics.objects.create(
            document=self.document,
            title="Topic 2",
            weightage=40.0
        )
        self.topic2.content.add(*self.embeddings[2:])

        # URLs
        self.list_url = reverse('blueprint-topics-list')
        self.detail_url = reverse('blueprint-topics-detail', args=[self.topic1.id])
        self.content_details_url = reverse('blueprint-topics-content-details', args=[self.topic1.id])
        self.by_document_url = reverse('blueprint-topics-by-document')
        self.top_topics_url = reverse('blueprint-topics-top-topics')

    def test_list_topics(self):
        """Test retrieving a list of topics"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['title'], 'Topic 1')  # Should be ordered by weightage

    def test_retrieve_topic(self):
        """Test retrieving a single topic"""
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Topic 1')
        self.assertEqual(response.data['weightage'], '60.00')
        self.assertIn('content_details', response.data)

    def test_create_topic(self):
        """Test creating a new topic"""
        data = {
            'document': self.document.id,
            'title': 'New Topic',
            'weightage': 25.0,
            'content': [self.embeddings[0].id, self.embeddings[1].id]
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BlueprintTopics.objects.count(), 3)
        self.assertEqual(response.data['title'], 'New Topic')

    def test_update_topic(self):
        """Test updating a topic"""
        data = {
            'document': self.document.id,
            'title': 'Updated Topic 1',
            'weightage': 70.0,
            'content': [e.id for e in self.embeddings]
        }
        response = self.client.put(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.topic1.refresh_from_db()
        self.assertEqual(self.topic1.title, 'Updated Topic 1')
        self.assertEqual(float(self.topic1.weightage), 70.0)
        self.assertEqual(self.topic1.content.count(), 5)

    def test_delete_topic(self):
        """Test deleting a topic"""
        response = self.client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(BlueprintTopics.objects.count(), 1)

    def test_content_details(self):
        """Test getting content details for a topic"""
        response = self.client.get(self.content_details_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('content_details', response.data)
        self.assertEqual(len(response.data['content_details']), 3)

        # Verify that full text content is returned
        for i, content_detail in enumerate(response.data['content_details']):
            self.assertIn('text', content_detail)
            self.assertEqual(content_detail['text'], f"This is test chunk {i}")
            # Verify preview is still included for backward compatibility
            self.assertIn('preview', content_detail)

    def test_by_document(self):
        """Test getting topics by document"""
        response = self.client.get(f"{self.by_document_url}?document_id={self.document.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_by_document_missing_param(self):
        """Test getting topics by document without document_id parameter"""
        response = self.client.get(self.by_document_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_top_topics(self):
        """Test getting top topics"""
        response = self.client.get(self.top_topics_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['title'], 'Topic 1')  # Highest weightage first

    def test_filter_by_document(self):
        """Test filtering topics by document"""
        response = self.client.get(f"{self.list_url}?document={self.document.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_filter_by_min_weightage(self):
        """Test filtering topics by minimum weightage"""
        response = self.client.get(f"{self.list_url}?min_weightage=50")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], 'Topic 1')

    def test_search_by_title(self):
        """Test searching topics by title"""
        response = self.client.get(f"{self.list_url}?search=Topic 1")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], 'Topic 1')
