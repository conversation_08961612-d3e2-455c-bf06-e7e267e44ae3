from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import ChatSession, ChatMessage
from .serializers import (
    ChatSessionSerializer,
    ChatMessageSerializer,
    ChatRequestSerializer,
    ChatResponseSerializer
)
from users.utils import increment_chat_count, get_usage_stats, UsageLimitExceeded
import requests
import os

class ChatSessionViewSet(viewsets.ModelViewSet):
    serializer_class = ChatSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        session = self.get_object()
        message = request.data.get('message')
        model = request.data.get('model', 'openai')
        context = request.data.get('context')

        if not message:
            return Response(
                {"error": "Message is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Call FastAPI inference endpoint
            inference_url = f"http://{os.getenv('FASTAPI_HOST', 'localhost:8001')}/infer"
            response = requests.post(
                inference_url,
                json={
                    "model": model,
                    "message": message,
                    "context": context
                },
                headers={"Authorization": f"Bearer {request.auth}"}
            )
            response.raise_for_status()
            inference_data = response.json()

            # Create user message
            user_message = ChatMessage.objects.create(
                session=session,
                role='user',
                content=message,
                model=model
            )

            # Create assistant message
            assistant_message = ChatMessage.objects.create(
                session=session,
                role='assistant',
                content=inference_data['response'],
                model=model,
                tokens=inference_data['tokens']
            )

            # Update session title if it's the first message
            if not session.title:
                session.title = message[:50] + "..." if len(message) > 50 else message
                session.save()

            return Response({
                "session_id": session.id,
                "message": inference_data['response'],
                "model": model,
                "tokens": inference_data['tokens'],
                "usage_stats": inference_data['usage_stats']
            })

        except requests.exceptions.RequestException as e:
            return Response(
                {"error": f"Error calling inference service: {str(e)}"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        except UsageLimitExceeded as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ChatMessageViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = ChatMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        session_id = self.kwargs.get('session_id')
        session = get_object_or_404(ChatSession, id=session_id, user=self.request.user)
        return ChatMessage.objects.filter(session=session).order_by('created_at')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['session_id'] = self.kwargs.get('session_id')
        return context


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_message_view(request):
    """
    Direct endpoint for sending chat messages.
    Compatible with frontend API calls to /chat/message/
    """
    message = request.data.get('message')
    document_id = request.data.get('document_id')
    model = request.data.get('model', 'openai')

    if not message:
        return Response(
            {"error": "Message is required"},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Check and increment chat count
        increment_chat_count(request.user)

        # Get or create a default session for the user
        session, created = ChatSession.objects.get_or_create(
            user=request.user,
            is_active=True,
            defaults={'title': message[:50] + "..." if len(message) > 50 else message}
        )

        # Prepare context if document_id is provided
        context = ""
        if document_id:
            try:
                from documents.models import Document, DocumentEmbedding
                document = Document.objects.get(id=document_id, user=request.user)
                chunks = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')[:5]  # Limit context
                context = "\n\n".join(chunk.text_chunk for chunk in chunks)
            except Document.DoesNotExist:
                pass  # Continue without context if document not found

        # Call FastAPI inference endpoint
        inference_url = f"http://{os.getenv('FASTAPI_HOST', 'localhost:8001')}/infer"
        response = requests.post(
            inference_url,
            json={
                "model": model,
                "message": message,
                "context": context
            },
            headers={"Authorization": f"Bearer {request.auth}"},
            timeout=30
        )
        response.raise_for_status()
        inference_data = response.json()

        # Create user message
        user_message = ChatMessage.objects.create(
            session=session,
            role='user',
            content=message,
            model=model
        )

        # Create assistant message
        assistant_message = ChatMessage.objects.create(
            session=session,
            role='assistant',
            content=inference_data['response'],
            model=model,
            tokens=inference_data.get('tokens', 0)
        )

        return Response({
            "session_id": session.id,
            "message": inference_data['response'],
            "model": model,
            "tokens": inference_data.get('tokens', 0),
            "usage_stats": inference_data.get('usage_stats', {})
        })

    except requests.exceptions.RequestException as e:
        return Response(
            {"error": f"Error calling inference service: {str(e)}"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )
    except UsageLimitExceeded as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_429_TOO_MANY_REQUESTS
        )
    except Exception as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_chat_history_view(request):
    """
    Direct endpoint for getting chat history.
    Compatible with frontend API calls to /chat/history/
    """
    try:
        # Get all sessions for the user
        sessions = ChatSession.objects.filter(user=request.user).order_by('-updated_at')

        # Get recent messages from all sessions
        recent_messages = []
        for session in sessions[:5]:  # Limit to 5 most recent sessions
            messages = ChatMessage.objects.filter(session=session).order_by('-created_at')[:10]  # Last 10 messages per session
            for message in messages:
                recent_messages.append({
                    "id": message.id,
                    "session_id": session.id,
                    "session_title": session.title,
                    "role": message.role,
                    "content": message.content,
                    "model": message.model,
                    "created_at": message.created_at,
                    "tokens": message.tokens
                })

        # Sort by creation time (most recent first)
        recent_messages.sort(key=lambda x: x['created_at'], reverse=True)

        return Response({
            "sessions": ChatSessionSerializer(sessions, many=True).data,
            "recent_messages": recent_messages[:50]  # Limit to 50 most recent messages
        })

    except Exception as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )