import openai
import google.generativeai as genai
import os
from typing import Op<PERSON>, <PERSON>ple
from fastapi import HTTPException, status


openai.api_key = os.getenv("OPENAI_API_KEY")
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))

async def get_openai_response(message: str, context: Optional[str] = None) -> Tuple[str, int]:
    """Get response from OpenAI API"""
    messages = []
    if context:
        messages.append({"role": "system", "content": context})
    messages.append({"role": "user", "content": message})

    try:
        response = await openai.ChatCompletion.acreate(
            model="gpt-3.5-turbo",
            messages=messages
        )
        return response.choices[0].message.content, response.usage.total_tokens
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OpenAI API error: {str(e)}"
        )

async def get_gemini_response(message: str, context: Optional[str] = None) -> <PERSON><PERSON>[str, int]:
    """Get response from Google Gemini API"""
    try:
        gemini_model = genai.GenerativeModel('gemini-pro')
        prompt = message
        if context:
            prompt = f"Context: {context}\n\nMessage: {message}"

        response = await gemini_model.generate_content_async(prompt)
        # Estimate tokens for Gemini (since it doesn't provide token count)
        tokens = len(prompt.split()) + len(response.text.split())
        return response.text, tokens
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Gemini API error: {str(e)}"
        )