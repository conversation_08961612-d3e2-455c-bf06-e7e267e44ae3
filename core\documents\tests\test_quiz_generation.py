from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from documents.models import Document, DocumentEmbedding, Quiz
import json
import os
import tempfile
from unittest.mock import patch, MagicMock
from django.conf import settings

User = get_user_model()

class QuizGenerationTests(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a test document
        self.document = Document.objects.create(
            user=self.user,
            title='Test Document',
            file='test.pdf',
            processing_status='completed'
        )
        
        # Create some test embeddings
        self.embedding = DocumentEmbedding.objects.create(
            document=self.document,
            text_chunk='This is a test document about artificial intelligence.',
            embedding=[0.1, 0.2, 0.3],
            chunk_number=1
        )
        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # URLs for testing
        self.quiz_list_url = reverse('quiz-list')
        self.quiz_generate_url = reverse('quiz-generate')
        
    @patch('requests.post')
    def test_automatic_quiz_generation(self, mock_post):
        """Test that quizzes are automatically generated when none exist"""
        # Mock the response from the FastAPI server
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'message': 'Quiz generated successfully',
            'document_id': self.document.id,
            'questions': [
                {'question': 'What is AI?', 'answer': 'Artificial Intelligence'},
                {'question': 'What is ML?', 'answer': 'Machine Learning'}
            ],
            'status': 'completed',
            'model': 'openai',
            'tokens': 100
        }
        mock_post.return_value = mock_response
        
        # Make a request to get quizzes for the document
        response = self.client.get(f'{self.quiz_list_url}?document={self.document.id}')
        
        # Check that the request was successful
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that the quiz was generated
        self.assertEqual(Quiz.objects.filter(document=self.document).count(), 2)
        
        # Check that the quiz questions match the mock response
        quizzes = Quiz.objects.filter(document=self.document)
        self.assertEqual(quizzes[0].question, 'What is AI?')
        self.assertEqual(quizzes[0].answer, 'Artificial Intelligence')
        self.assertEqual(quizzes[1].question, 'What is ML?')
        self.assertEqual(quizzes[1].answer, 'Machine Learning')
        
    @patch('requests.post')
    def test_explicit_quiz_generation(self, mock_post):
        """Test explicit quiz generation using the generate endpoint"""
        # Mock the response from the FastAPI server
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'message': 'Quiz generated successfully',
            'document_id': self.document.id,
            'questions': [
                {'question': 'What is AI?', 'answer': 'Artificial Intelligence'},
                {'question': 'What is ML?', 'answer': 'Machine Learning'},
                {'question': 'What is DL?', 'answer': 'Deep Learning'}
            ],
            'status': 'completed',
            'model': 'gemini',
            'tokens': 150
        }
        mock_post.return_value = mock_response
        
        # Make a request to generate quizzes for the document
        response = self.client.post(
            self.quiz_generate_url,
            {'document_id': self.document.id, 'llm_model': 'gemini', 'num_questions': 3},
            format='json'
        )
        
        # Check that the request was successful
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that the quiz was generated
        self.assertEqual(Quiz.objects.filter(document=self.document).count(), 3)
        
        # Check that the quiz questions match the mock response
        quizzes = Quiz.objects.filter(document=self.document)
        self.assertEqual(quizzes[0].question, 'What is AI?')
        self.assertEqual(quizzes[0].answer, 'Artificial Intelligence')
        self.assertEqual(quizzes[1].question, 'What is ML?')
        self.assertEqual(quizzes[1].answer, 'Machine Learning')
        self.assertEqual(quizzes[2].question, 'What is DL?')
        self.assertEqual(quizzes[2].answer, 'Deep Learning')
