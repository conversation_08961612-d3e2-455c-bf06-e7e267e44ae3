"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check } from "lucide-react"
import { useRouter } from "next/navigation"
import { LayoutWithSidebar } from "@/components/layout-with-sidebar"

export default function SubscriptionPage() {
  const router = useRouter()

  return (
    <LayoutWithSidebar>
      <div className="max-w-6xl mx-auto py-8 px-4">
        <Button variant="ghost" onClick={() => router.back()} className="mb-8">
          ← Back
        </Button>

        <h1 className="text-3xl font-bold text-center mb-4">Choose Your Plan</h1>
        <p className="text-center text-muted-foreground mb-12">Select the plan that best fits your learning needs</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Daily Plan */}
          <Card className="border-border bg-card hover:border-purple-500 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-xl">Daily Plan</CardTitle>
              <CardDescription>Perfect for quick learning sessions</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$1.99</span>
                <span className="text-muted-foreground"> / day</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Unlimited uploads</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Basic AI features</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>24-hour access</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Subscribe Now</Button>
            </CardFooter>
          </Card>

          {/* Monthly Plan */}
          <Card className="border-purple-500 bg-card relative">
            <div className="absolute top-0 right-0 bg-purple-500 text-white text-xs px-3 py-1 rounded-bl-lg rounded-tr-lg">
              POPULAR
            </div>
            <CardHeader>
              <CardTitle className="text-xl">Monthly Plan</CardTitle>
              <CardDescription>Our most popular option</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$19.99</span>
                <span className="text-muted-foreground"> / month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Unlimited uploads</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Advanced AI features</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>5 custom spaces</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Priority support</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Subscribe Now</Button>
            </CardFooter>
          </Card>

          {/* Yearly Plan */}
          <Card className="border-border bg-card hover:border-purple-500 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-xl">Yearly Plan</CardTitle>
              <CardDescription>Best value for committed learners</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">$199.99</span>
                <span className="text-muted-foreground"> / year</span>
                <div className="text-sm text-purple-500 font-medium">Save $40</div>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Everything in Monthly</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Unlimited spaces</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Advanced analytics</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-purple-500 mr-2" />
                  <span>Dedicated support</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Subscribe Now</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </LayoutWithSidebar>
  )
}
