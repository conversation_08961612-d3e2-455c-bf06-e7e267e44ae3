# Generated by Django 4.2.20 on 2025-06-10 12:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0006_studentperformance"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="studentperformance",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Student Performance",
                "verbose_name_plural": "Student Performances",
            },
        ),
        migrations.AlterUniqueTogether(
            name="studentperformance",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="studentperformance",
            name="time_taken",
            field=models.PositiveIntegerField(
                default=0, help_text="Time taken to complete the quiz (in seconds)"
            ),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name="studentperformance",
            index=models.Index(
                fields=["created_at"], name="users_stude_created_f685e1_idx"
            ),
        ),
        migrations.RemoveField(
            model_name="studentperformance",
            name="attempts",
        ),
        migrations.RemoveField(
            model_name="studentperformance",
            name="updated_at",
        ),
    ]
