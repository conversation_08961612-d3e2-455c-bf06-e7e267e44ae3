"use client"

import { useState } from "react"
import UploadContent from "./upload"
import PasteContent from "./paste"
import RecordContent from "./record"

interface UploadedData {
  type: string
  name?: string
  size?: string
  content?: string
  duration?: string
}

const ProcessPage = () => {
  const [type, setType] = useState<string>("upload")
  const [uploadedData, setUploadedData] = useState<UploadedData | null>(null)

  const renderUploadedData = () => {
    if (uploadedData) {
      if (uploadedData.type === "file") {
        return (
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className="aspect-[3/4] bg-neutral-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <p className="text-lg">PDF Document</p>
                <p className="text-sm text-neutral-500">{uploadedData.size}</p>
              </div>
            </div>
          </div>
        )
      } else if (uploadedData.type === "text") {
        return (
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">Pasted Content</h2>
            <div className="bg-neutral-800 rounded-lg p-4">
              <p>{uploadedData.content}</p>
            </div>
          </div>
        )
      } else if (uploadedData.type === "audio") {
        return (
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className="bg-neutral-800 rounded-lg p-4 flex flex-col items-center">
              <div className="w-full h-24 bg-neutral-700 rounded-md mb-4"></div>
              <audio controls className="w-full">
                <source src="#" type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
              <p className="text-sm text-neutral-500 mt-2">Duration: {uploadedData.duration}</p>
            </div>
          </div>
        )
      }
    }

    // If no uploaded data yet, show the input component
    switch (type) {
      case "upload":
        return <UploadContent />
      case "paste":
        return <PasteContent />
      case "record":
        return <RecordContent />
      default:
        return <UploadContent />
    }
  }

  const renderInputComponent = () => {
    return renderUploadedData()
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-2">
      <h1 className="text-4xl font-bold mb-8">Process Content</h1>

      <div className="flex space-x-4 mb-8">
        <button
          className={`px-4 py-2 rounded-md ${type === "upload" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"}`}
          onClick={() => setType("upload")}
        >
          Upload
        </button>
        <button
          className={`px-4 py-2 rounded-md ${type === "paste" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"}`}
          onClick={() => setType("paste")}
        >
          Paste
        </button>
        <button
          className={`px-4 py-2 rounded-md ${type === "record" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"}`}
          onClick={() => setType("record")}
        >
          Record
        </button>
      </div>

      {renderInputComponent()}
    </div>
  )
}

export default ProcessPage
