"""
<PERSON><PERSON><PERSON> to download required NLTK data.
Run this script once to ensure all required NLTK data is available.
"""
import nltk
import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def download_nltk_data():
    """Download all required NLTK data packages."""
    try:
        # Create nltk_data directory in the current directory if it doesn't exist
        nltk_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'nltk_data')
        os.makedirs(nltk_data_dir, exist_ok=True)
        
        # Set NLTK data path to include our custom directory
        nltk.data.path.append(nltk_data_dir)
        
        # Download required data
        logging.info(f"Downloading NLTK data to {nltk_data_dir}")
        nltk.download('punkt', download_dir=nltk_data_dir)
        
        # Verify installation
        from nltk.tokenize import sent_tokenize, word_tokenize
        test_text = "This is a test sentence. This is another test sentence."
        sentences = sent_tokenize(test_text)
        words = word_tokenize(sentences[0])
        
        logging.info(f"NLTK test - Sentences: {sentences}")
        logging.info(f"NLTK test - Words: {words}")
        logging.info("NLTK data downloaded and verified successfully.")
        
        return True
    except Exception as e:
        logging.error(f"Error downloading NLTK data: {str(e)}")
        return False

if __name__ == "__main__":
    success = download_nltk_data()
    sys.exit(0 if success else 1)
