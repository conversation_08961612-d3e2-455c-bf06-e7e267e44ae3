from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    DocumentViewSet, DocumentEmbeddingViewSet, FlashcardViewSet,
    FlowchartViewSet, QuizViewSet, BlueprintTopicsViewSet
)
from .api import (
    store_embeddings, store_topics, update_document_status,
    find_relevant_embeddings, get_document_embeddings
)

router = DefaultRouter()
router.register(r'', DocumentViewSet, basename='document')
router.register(r'embeddings', DocumentEmbeddingViewSet, basename='document-embedding')
router.register(r'flashcards', FlashcardViewSet, basename='flashcard')
router.register(r'flowcharts', FlowchartViewSet, basename='flowchart')
router.register(r'qa', QuizViewSet, basename='quiz')
router.register(r'topics', BlueprintTopicsViewSet, basename='blueprint-topics')

urlpatterns = [
    # Original routes
    path('', include(router.urls)),

    # API endpoints for FastAPI integration
    path('<int:document_id>/store-embeddings/', store_embeddings, name='store-embeddings'),
    path('<int:document_id>/store-topics/', store_topics, name='store-topics'),
    path('<int:document_id>/update-status/', update_document_status, name='update-document-status'),
    path('<int:document_id>/find-relevant-embeddings/', find_relevant_embeddings, name='find-relevant-embeddings'),
    path('<int:document_id>/embeddings/', get_document_embeddings, name='get-document-embeddings'),
]

'''
GET /documents/ - List all documents for the current user
POST /documents/ - Create a new document (using the standard create method)
GET /documents/{id}/ - Retrieve a specific document
PUT /documents/{id}/ - Update a document
PATCH /documents/{id}/ - Partially update a document
DELETE /documents/{id}/ - Delete a document
GET /documents/{id}/processing_status/ - Get document processing status (custom action)
POST /documents/upload/ - Upload a document file (custom action)
GET /documents/{id}/blueprint/ - Get document blueprint (custom action)
POST /documents/{id}/blueprint/ - Upload document blueprint (custom action)
'''