"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send, User, Paperclip, MessageSquare } from "lucide-react"
import { Avatar } from "@/components/ui/avatar"
import NextImage from "next/image"
import { useTheme } from "@/components/theme-provider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { processBlueprint } from "@/lib/api"

type Message = {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface Topic {
  title: string;
  weightage: number;
}

export function BlueprintInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const blueprintContainerRef = useRef<HTMLDivElement>(null)
  const [files, setFiles] = useState<File[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const { theme } = useTheme()

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const handleSendMessage = async () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsLoading(true)

    try {
      // Call the API to process the blueprint
      const response = await processBlueprint(1, input) // Replace 1 with actual document ID
      
      // Add assistant message with the response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `Blueprint processed successfully. Topics identified: ${response.topics.map((t: Topic) => t.title).join(', ')}`,
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "Error processing blueprint. Please try again.",
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleAttachmentClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      // Here you would typically upload the file and then reference it in a message
      const userMessage: Message = {
        id: Date.now().toString(),
        content: `Attached blueprint file: ${file.name}`,
        role: "user",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, userMessage])

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Simulate AI response
      setIsLoading(true)
      setTimeout(() => {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Blueprint successfully uploaded. I'll focus on the areas specified in ${file.name}.`,
          role: "assistant",
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, assistantMessage])
        setIsLoading(false)
      }, 1000)
    }
  }

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const isEmpty = messages.length === 0 && !isLoading

  return (
    <div className="flex flex-col h-full bg-black" ref={blueprintContainerRef}>
      <div className="p-4 border-b border-neutral-800">
        <h2 className="text-xl font-medium text-center">Blueprint</h2>
        {isEmpty && (
          <p className="text-center text-sm text-muted-foreground mt-1">
            Provide special instructions for Cognimosity to focus on specific topics or areas
          </p>
        )}
      </div>

      <ScrollArea className="flex-1">
        {isEmpty ? (
          <div className="h-full flex flex-col items-center justify-center">
            <div className="p-6 rounded-full bg-neutral-800 mb-4">
              <MessageSquare className="h-12 w-12 text-muted-foreground" />
            </div>
            <div className="rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md">
              <h3 className="font-medium text-purple-500 mb-2">What is a Blueprint?</h3>
              <p className="text-sm">
                A Blueprint helps Cognimosity understand which topics or areas you want to prioritize. This will
                influence how content is analyzed, summarized, and presented across all features.
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4 p-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                <div className={`flex gap-3 max-w-[80%] ${message.role === "user" ? "flex-row-reverse" : "flex-row"}`}>
                  {message.role === "assistant" ? (
                    <Avatar className="bg-purple-600">
                      <div className="relative h-5 w-5">
                        <NextImage
                          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                          alt="Cognimosity Logo"
                          fill
                          className="object-contain"
                        />
                      </div>
                    </Avatar>
                  ) : (
                    <Avatar className="bg-neutral-700">
                      <User className="h-5 w-5" />
                    </Avatar>
                  )}
                  <div
                    className={`rounded-lg p-3 ${
                      message.role === "user" ? "bg-purple-600 text-white" : "bg-neutral-800 text-neutral-100"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex gap-3">
                  <Avatar className="bg-purple-600">
                    <div className="relative h-5 w-5">
                      <NextImage
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                        alt="Cognimosity Logo"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </Avatar>
                  <div className="rounded-lg p-3 bg-neutral-800 text-neutral-100">
                    <div className="flex space-x-2">
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="h-2 w-2 rounded-full bg-neutral-400 animate-bounce"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>

      <div className="p-4 border-t border-neutral-800 bg-black">
        <div className="flex items-center gap-2 rounded-md bg-neutral-900 p-2">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Describe what topics or areas you want Cognimosity to focus on..."
            className="min-h-[40px] max-h-[120px] bg-transparent border-0 focus-visible:ring-0 resize-none py-2 px-2 flex-1"
          />

          <div className="flex items-center gap-1">
            <Button
              onClick={handleAttachmentClick}
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-neutral-400 hover:text-white hover:bg-transparent"
              title="Attach file"
            >
              <Paperclip className="h-5 w-5" />
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept=".pdf,.doc,.docx,.txt"
              />
            </Button>

            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading}
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-purple-500 hover:text-purple-400 hover:bg-transparent"
              title="Send"
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 