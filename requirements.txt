# Django and REST Framework
Django>=4.2.0,<5.0
djangorestframework>=3.14.0
django-cors-headers>=4.3.0
django-filter==23.5
django-celery-beat==2.5.0

# Database (Optional - for PostgreSQL)
# psycopg2-binary==2.9.9

# Cache and Background Tasks
redis==5.0.1
celery==5.3.6

# API and Authentication
python-dotenv>=1.0.0
requests==2.31.0
PyJWT==2.8.0

# File Handling
Pillow
python-magic==0.4.27

# Email
django-sendgrid-v5==0.8.1

# Payment Processing
razorpay

# LLM Integration
openai>=1.0.0
google-generativeai>=0.3.0

# PDF and Text Processing
PyPDF2>=3.0.0
nltk>=3.8.1
sentence-transformers>=2.2.0
numpy>=1.24.0
aiohttp>=3.9.0

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0

# Development Tools
black==24.1.1
flake8==7.0.0
isort==5.13.2


djangorestframework-simplejwt
setuptools
fastapi
uvicorn

PyMuPDF
python-docx