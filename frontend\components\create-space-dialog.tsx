"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface CreateSpaceDialogProps {
  open: boolean
  setOpen: (open: boolean) => void
}

export function CreateSpaceDialog({ open, setOpen }: CreateSpaceDialogProps) {
  const [spaceName, setSpaceName] = useState("")

  const handleCreateSpace = () => {
    if (spaceName.trim()) {
      // Here you would typically save the new space to your backend
      console.log("Creating space:", spaceName)
      setOpen(false)
      setSpaceName("")
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md bg-background border-border">
        <DialogHeader>
          <DialogTitle>Create New Space</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Space Name</Label>
              <Input
                id="name"
                placeholder="Enter space name"
                value={spaceName}
                onChange={(e) => setSpaceName(e.target.value)}
                className="bg-background border-border"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button
                className="bg-purple-600 hover:bg-purple-700"
                onClick={handleCreateSpace}
                disabled={!spaceName.trim()}
              >
                Create Space
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
