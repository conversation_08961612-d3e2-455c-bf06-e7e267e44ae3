"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Download, ZoomIn, ZoomOut, RotateCcw, Maximize, MessageSquare } from "lucide-react"
import { motion } from "framer-motion"
import { useTheme } from "@/components/theme-provider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { generateFlowchart } from "@/lib/api"
import mermaid from 'mermaid'

// Sample Mermaid flowchart data
const sampleFlowchart = `
graph TD
    A["Start: Machine Learning Process"] --> B{"Data Available?"}
    B -->|Yes| C["Data Collection"]
    B -->|No| D["Gather Data"]
    D --> C
    C --> E["Data Preprocessing"]
    E --> F["Feature Selection"]
    F --> G["Split Data"]
    G --> H["Training Set"]
    G --> I["Testing Set"]
    H --> J["Choose Algorithm"]
    J --> K["Train Model"]
    K --> L["Evaluate Model"]
    I --> L
    L --> M{"Performance Good?"}
    M -->|No| N["Tune Parameters"]
    N --> K
    M -->|Yes| O["Deploy Model"]
    O --> P["Monitor Performance"]
    P --> Q["End"]
    
    style A fill:#6366f1,stroke:#4f46e5,color:#ffffff
    style O fill:#10b981,stroke:#059669,color:#ffffff
    style Q fill:#ef4444,stroke:#dc2626,color:#ffffff
    style M fill:#f59e0b,stroke:#d97706,color:#ffffff
`

export function FlowchartInterface() {
  const [zoom, setZoom] = useState(1)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [flowchartData, setFlowchartData] = useState("")
  const flowchartRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()
  const flowchartContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize mermaid
    mermaid.initialize({
      startOnLoad: true,
      theme: theme === "dark" ? "dark" : "default",
      themeVariables:
        theme === "dark"
          ? {
              primaryColor: "#6366f1",
              primaryTextColor: "#ffffff",
              primaryBorderColor: "#4f46e5",
              lineColor: "#8b5cf6",
              secondaryColor: "#7c3aed",
              tertiaryColor: "#a855f7",
              background: "#1e1b4b",
              mainBkg: "#312e81",
              secondBkg: "#3730a3",
              tertiaryBkg: "#4338ca",
            }
          : {
              primaryColor: "#818cf8",
              primaryTextColor: "#ffffff",
              primaryBorderColor: "#6366f1",
              lineColor: "#6366f1",
              secondaryColor: "#c7d2fe",
              tertiaryColor: "#e0e7ff",
              background: "#ffffff",
              mainBkg: "#f3f4f6",
              secondBkg: "#e5e7eb",
              tertiaryBkg: "#f9fafb",
            },
    })

    loadFlowchart()
  }, [theme])

  const loadFlowchart = async () => {
    try {
      setIsLoading(true)
      const response = await generateFlowchart(1) // Replace 1 with actual document ID
      setFlowchartData(response.flowchart || sampleFlowchart)
      
      if (flowchartRef.current) {
        flowchartRef.current.innerHTML = `<div class="mermaid">${flowchartData}</div>`
        await mermaid.run()
      }
    } catch (error) {
      console.error("Failed to load flowchart:", error)
      setError("Failed to load flowchart visualization. Please try again later.")
      // Use sample flowchart as fallback
      setFlowchartData(sampleFlowchart)
      if (flowchartRef.current) {
        flowchartRef.current.innerHTML = `<div class="mermaid">${sampleFlowchart}</div>`
        await mermaid.run()
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.2, 3))
  }

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.2, 0.5))
  }

  const handleReset = () => {
    setZoom(1)
  }

  const handleDownload = () => {
    // In a real implementation, this would export the flowchart as SVG/PNG
    console.log("Download flowchart")
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  return (
    <div className="flex flex-col h-full bg-black" ref={flowchartContainerRef}>
      <div className="p-4 border-b border-neutral-800">
        <h2 className="text-xl font-medium text-center">Flowchart</h2>
        <p className="text-center text-sm text-muted-foreground mt-1">Machine Learning Process Visualization</p>
      </div>

      <div className="flex items-center justify-between px-4 py-2 border-b border-neutral-800">
        <div>
          <span className="text-sm text-muted-foreground">Zoom: {Math.round(zoom * 100)}%</span>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleZoomOut} size="sm" variant="outline" className="h-8 w-8 p-0" disabled={zoom <= 0.5}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button onClick={handleReset} size="sm" variant="outline" className="h-8 w-8 p-0">
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button onClick={handleZoomIn} size="sm" variant="outline" className="h-8 w-8 p-0" disabled={zoom >= 3}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button onClick={toggleFullscreen} size="sm" variant="outline" className="h-8 w-8 p-0">
            <Maximize className="h-4 w-4" />
          </Button>
          <Button onClick={handleDownload} size="sm" className="bg-purple-600 hover:bg-purple-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
              <p className="text-muted-foreground">Loading flowchart...</p>
            </div>
          </div>
        ) : error ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center max-w-md p-6">
              <MessageSquare className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-red-500 mb-2">Error Loading Flowchart</h3>
              <p className="text-muted-foreground">{error}</p>
            </div>
          </div>
        ) : (
          <div className="p-6">
            <Card className="bg-neutral-800 border-neutral-700 p-6 overflow-hidden">
              <div className="overflow-auto">
                <motion.div
                  ref={flowchartRef}
                  className="flex items-center justify-center min-h-[500px]"
                  style={{
                    transform: `scale(${zoom})`,
                    transformOrigin: "center top",
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Mermaid flowchart will be rendered here */}
                </motion.div>
              </div>
            </Card>
          </div>
        )}
      </ScrollArea>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-6" onClick={toggleFullscreen}>
          <div
            className="w-full h-full max-w-6xl max-h-[90vh] bg-neutral-800 border border-neutral-700 rounded-lg overflow-auto p-6"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-medium">Machine Learning Process Flowchart</h3>
            </div>
            <div className="overflow-auto">
              <motion.div
                ref={flowchartRef}
                className="flex items-center justify-center min-h-[500px]"
                style={{
                  transform: `scale(${zoom})`,
                  transformOrigin: "center top",
                }}
                transition={{ duration: 0.3 }}
              >
                {/* Mermaid flowchart will be rendered here */}
              </motion.div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 